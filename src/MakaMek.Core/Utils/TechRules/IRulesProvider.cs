using Sanet.MakaMek.Core.Data.Game.Mechanics;
using Sanet.MakaMek.Core.Models.Map;
using Sanet.MakaMek.Core.Models.Map.Terrains;
using Sanet.MakaMek.Core.Models.Units;
using Sanet.MakaMek.Core.Models.Units.Components.Weapons;

namespace Sanet.MakaMek.Core.Utils.TechRules;

public interface IRulesProvider
{
    Dictionary<PartLocation, int> GetStructureValues(int tonnage);

    /// <summary>
    /// Gets the modifier for the attacker's movement type
    /// </summary>
    int GetAttackerMovementModifier(MovementType movementType);

    /// <summary>
    /// Gets the modifier based on how many hexes the target has moved
    /// </summary>
    int GetTargetMovementModifier(int hexesMoved);

    /// <summary>
    /// Gets the modifier for firing at a specific range bracket
    /// </summary>
    int GetRangeModifier(WeaponRange  rangeType, int rangeValue, int distance);

    /// <summary>
    /// Gets the to-hit modifier for a specific terrain type
    /// </summary>
    int GetTerrainToHitModifier(MakaMekTerrains terrainType);

    /// <summary>
    /// Gets the aimed shot modifier for targeting a specific body part
    /// </summary>
    /// <param name="targetLocation">The body part being targeted</param>
    /// <returns>The modifier value for aimed shots at the specified location</returns>
    int GetAimedShotModifier(PartLocation targetLocation);

    /// <summary>
    /// Gets the 2D6 roll values that result in a successful aimed shot
    /// </summary>
    /// <returns>Array of dice roll values that indicate aimed shot success</returns>
    int[] GetAimedShotSuccessValues();

    /// <summary>
    /// Gets the modifier for firing at a secondary target
    /// </summary>
    /// <param name="isFrontArc">Whether the target is in the front arc</param>
    /// <returns>The modifier value to apply</returns>
    int GetSecondaryTargetModifier(bool isFrontArc);
    
    /// <summary>
    /// Determines the hit location on a unit based on dice roll and attack direction
    /// </summary>
    /// <param name="diceResult">The 2D6 roll result determining hit location</param>
    /// <param name="attackDirection">The direction from which the attack is coming</param>
    /// <returns>The part location that was hit</returns>
    PartLocation GetHitLocation(int diceResult, FiringArc attackDirection);
    
    /// <summary>
    /// Determines how many missiles hit the target based on the cluster hit table
    /// </summary>
    /// <param name="diceResult">The 2D6 roll result</param>
    /// <param name="weaponSize">The total number of missiles in the weapon (Clusters * ClusterSize)</param>
    /// <returns>The number of missiles that hit the target</returns>
    int GetClusterHits(int diceResult, int weaponSize);
    
    /// <summary>
    /// Determines the new facing direction after a fall based on a dice roll
    /// </summary>
    /// <param name="roll">The D6 roll result (1-6)</param>
    /// <param name="currentFacing">The unit's current facing direction</param>
    /// <returns>The new facing direction after the fall</returns>
    HexDirection GetFacingAfterFall(int roll, HexDirection currentFacing);
    
    /// <summary>
    /// Determines the attack direction for hit location purposes after a fall
    /// </summary>
    /// <param name="roll">The D6 roll result (1-6)</param>
    /// <returns>The attack direction for hit location determination</returns>
    FiringArc GetAttackDirectionAfterFall(int roll);

    /// <summary>
    /// Gets the heat points generated by movement based on a movement type and points spent
    /// </summary>
    /// <param name="movementType">The type of movement used</param>
    /// <param name="movementPointSpent">The number of movement points spent</param>
    /// <returns>Heat points generated by movement</returns>
    int GetMovementHeatPoints(MovementType movementType, int movementPointSpent);

    int GetPilotingSkillRollModifier(PilotingSkillRollType psrType);

    int GetHeavyDamageThreshold();
}